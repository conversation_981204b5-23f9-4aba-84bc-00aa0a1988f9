<?php

namespace App\Mail;

use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class JobNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * The job notification campaign instance.
     *
     * @var \App\Models\JobNotificationCampaign
     */
    public $campaign;

    /**
     * The job notification recipient instance.
     *
     * @var \App\Models\JobNotificationRecipient
     */
    public $recipient;

    /**
     * Create a new message instance.
     *
     * @param  \App\Models\JobNotificationCampaign  $campaign
     * @param  \App\Models\JobNotificationRecipient  $recipient
     * @return void
     */
    public function __construct(JobNotificationCampaign $campaign, JobNotificationRecipient $recipient)
    {
        $this->campaign = $campaign;
        $this->recipient = $recipient;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $businessName = $this->recipient->business_name 
            ?? $this->recipient->business?->name 
            ?? 'Business Owner';

        $business = $this->recipient->business;
        $jobId = $this->campaign->job_id;
        $token = $this->campaign->admin_token;
        $linkedUser = null;
        $link = null;
        $sendTo = $this->recipient->business_email;

        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app'), '/');

        $linkedUser = \App\Models\User::where('email', $this->recipient->business_email)->first();

        // Build base link for both cases
        $link = $webAppDomain
            . '/auth-email?token='
            . urlencode((string) $token)
            . '&job_id='
            . urlencode((string) $jobId)
            . '&business_email='
            . urlencode($this->recipient->business_email);

        // If business doesn't have an account, append login mode
        if (!$linkedUser) {
            $link .= '&mode=login';
        }
        
        // Use linked user email if exists, otherwise use business email
        $sendTo = $linkedUser ? $linkedUser->email : $this->recipient->business_email;

        return $this->to($sendTo)
            ->subject('Looking for cleaner in ' . ($this->campaign->job_zip_code ?? $this->campaign->job_address ?? 'your area') . ' ASAP')
            ->markdown('emails.job-notification')
            ->with([
                'campaign' => $this->campaign,
                'recipient' => $this->recipient,
                'businessName' => $businessName,
                'jobTitle' => $this->campaign->job_title,
                'jobDescription' => $this->campaign->job_description,
                'jobBudget' => $this->campaign->job_budget,
                'jobZipCode' => $this->campaign->job_zip_code,
                'distance' => $this->recipient->distance,
                'link' => $link,
                'linkedUser' => $linkedUser,
                'jobId' => $jobId,
                'token' => $token,
                'adminContactEmail' => config('job_notification.admin_contact_email'),
                'adminContactPhone' => config('job_notification.admin_contact_phone'),
            ]);
    }
} 