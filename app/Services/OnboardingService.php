<?php

namespace App\Services;

use App\Models\User;
use App\Models\Address;
use App\Models\OnboardingProgress;
use App\Models\ProviderVerification;
use App\Models\Country;
use App\Models\State;
use App\Models\Service;
use App\Services\AssetService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;

class OnboardingService
{
    protected $assetService;

    public function __construct(AssetService $assetService)
    {
        $this->assetService = $assetService;
    }

    /**
     * Process complete onboarding data for a provider.
     *
     * @param array $data
     * @param User $provider
     * @param array $files
     * @return array
     */
    public function processOnboarding(array $data, User $provider, array $files = []): array
    {
        try {
            DB::beginTransaction();

            // Extract profile data
            $profileData = $data['onboardingData']['profile'] ?? [];

            // Update provider profile
            $this->updateProviderProfile($provider, $profileData);

            // Handle service selections
            $this->handleServiceSelections($provider, $profileData['selectedServices'] ?? []);

            // Create or update address
            $this->handleServiceAddress($provider, $profileData);

            // Handle file uploads
            $uploadedFiles = $this->handleFileUploads($provider, $files);

            // Create verification record
            $this->createVerificationRecord($provider, $profileData, $uploadedFiles);

            // Update or create onboarding progress
            $this->updateOnboardingProgress($provider);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Onboarding data processed successfully',
                'provider' => $provider->fresh(['onboardingProgress', 'addresses', 'verificationDocuments']),
                'uploaded_files' => $uploadedFiles
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Onboarding processing failed: ' . $e->getMessage(), [
                'provider_id' => $provider->id,
                'error' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Update provider profile with onboarding data.
     */
    protected function updateProviderProfile(User $provider, array $profileData): void
    {
        $updateData = [
            'name' => $profileData['fullName'] ?? $provider->name,
            'email' => $profileData['email'] ?? $provider->email,
            'phone' => $this->extractPhoneNumber($profileData['phone'] ?? $provider->phone),
            'company_name' => $profileData['businessName'] ?? null,
            'service_address' => $profileData['serviceAddress'] ?? null,
            'service_radius' => $profileData['serviceRadius'] ?? null,
            'selected_availability' => $profileData['selectedAvailability'] ?? null,
            'work_description' => $profileData['workDescription'] ?? null,
            'business_license_number' => $profileData['businessLicenseNumber'] ?? null,
            'tax_id' => $profileData['taxId'] ?? null,
            'business_registration_state' => $profileData['businessRegistrationState'] ?? null,
            'license_number' => $profileData['licenseNumber'] ?? null,
            'professional_certifications' => $profileData['professionalCertifications'] ?? null,
            'company_website' => $profileData['companyWebsite'] ?? null,
            'years_in_business' => $profileData['yearsInBusiness'] ?? null,
            'about_me' => $profileData['aboutMe'] ?? null,
            'auto_bidding' => $profileData['autoBidding'] ?? false,
        ];

        $provider->update(array_filter($updateData, function($value) {
            return $value !== null;
        }));
    }

    /**
     * Handle service selections by syncing expertise services.
     */
    protected function handleServiceSelections(User $provider, array $serviceIds): void
    {
        if (empty($serviceIds)) {
            return;
        }

        // Validate that all service IDs exist
        $validServiceIds = Service::whereIn('id', $serviceIds)->pluck('id')->toArray();

        // Sync the provider's expertise services
        $provider->expertise()->sync($validServiceIds);
    }

    /**
     * Handle service address creation/update.
     */
    protected function handleServiceAddress(User $provider, array $profileData): void
    {
        if (empty($profileData['serviceAddress'])) {
            return;
        }

        // Find or create primary address
        $address = Address::where('user_id', $provider->id)
            ->where('is_primary', 1)
            ->first();

        if (!$address) {
            $address = new Address();
            $address->user_id = $provider->id;
            $address->is_primary = 1;
        }

        $address->address = $profileData['serviceAddress'];
        $address->availability_radius = $profileData['serviceRadius'] ?? null;
        $address->type = 'service';
        $address->status = 1;

        // Pick a valid random country and a matching state from the database
        [$countryId, $stateId] = $this->selectRandomCountryAndStateIds();
        $address->country_id = $address->country_id ?? $countryId;
        $address->state_id = $address->state_id ?? $stateId;

        // You might want to geocode the address here to get lat/lng
        // For now, we'll leave them null
        $address->save();
    }

    /**
     * Select a random available country and a matching state.
     * Falls back to any random state if the chosen country has no states.
     * Throws an exception if required lookup tables are empty.
     */
    private function selectRandomCountryAndStateIds(): array
    {
        $countryId = Country::query()->inRandomOrder()->value('id');
        if (!$countryId) {
            throw new \RuntimeException('No countries found. Please seed CountriesSeeder.');
        }

        $stateId = State::query()
            ->where('country_id', $countryId)
            ->inRandomOrder()
            ->value('id');

        if (!$stateId) {
            // Fallback: any state
            $stateId = State::query()->inRandomOrder()->value('id');
        }

        if (!$stateId) {
            throw new \RuntimeException('No states found. Please seed StateSeeder.');
        }

        return [$countryId, $stateId];
    }

    /**
     * Handle file uploads.
     */
    protected function handleFileUploads(User $provider, array $files): array
    {
        $uploadedFiles = [];

        foreach ($files as $key => $file) {
            if ($file instanceof UploadedFile) {
                try {
                    $asset = $this->assetService->upload(
                        $file,
                        'onboarding_documents',
                        [
                            'uploaded_by' => $provider->id,
                            'document_type' => $key,
                            'original_name' => $file->getClientOriginalName()
                        ]
                    );

                    $uploadedFiles[$key] = $asset;

                    // Update provider with file references
                    if ($key === 'insuranceCertificate') {
                        $provider->update(['insurance_certificate' => $asset->uuid]);
                    }

                } catch (\Exception $e) {
                    Log::error("Failed to upload file {$key}: " . $e->getMessage());
                    throw $e;
                }
            }
        }

        return $uploadedFiles;
    }

    /**
     * Create verification record.
     */
    protected function createVerificationRecord(User $provider, array $profileData, array $uploadedFiles): void
    {
        $documents = [];
        foreach ($uploadedFiles as $asset) {
            $documents[] = $asset->uuid;
        }

        // Only create verification record if we have business license info or uploaded files
        if (!empty($profileData['businessLicenseNumber']) || !empty($uploadedFiles)) {
            $verificationData = [
                'provider_id' => $provider->id,
                'document_type' => 'business_license',
                'status' => 'pending',
                'business_license_number' => $profileData['businessLicenseNumber'] ?? null,
                'tax_id' => $profileData['taxId'] ?? null,
                'business_registration_state' => $profileData['businessRegistrationState'] ?? null,
                'submitted_at' => now(),
                'documents' => $documents,
            ];

            // Only add document_uuid if we have an actual uploaded file
            if (!empty($uploadedFiles['businessLicense'])) {
                $verificationData['document_uuid'] = $uploadedFiles['businessLicense']->uuid;
            }

            ProviderVerification::create($verificationData);
        }
    }

    /**
     * Update onboarding progress.
     */
    protected function updateOnboardingProgress(User $provider): void
    {
        $progress = OnboardingProgress::firstOrCreate(
            ['provider_id' => $provider->id],
            [
                'current_step' => 1,
                'is_complete' => false,
                'verification_status' => 'pending',
                'completion_percentage' => 0,
            ]
        );

        $progress->updateCompletionPercentage();
    }

    /**
     * Extract phone number from string (remove non-numeric characters).
     */
    protected function extractPhoneNumber(string $phone): ?int
    {
        $cleaned = preg_replace('/[^0-9]/', '', $phone);
        return $cleaned ? (int) $cleaned : null;
    }
}