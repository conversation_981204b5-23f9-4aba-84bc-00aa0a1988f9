<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Models\JobBooking;
use App\Models\Job;
use App\Models\Bid;
use App\Models\Booking;
use App\Models\Business;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class CustomerJobHistoryController extends Controller
{
    /**
     * Get all jobs created by customer through webhook notifications
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getCustomerJobHistory(Request $request): JsonResponse
    {
        try {
            // Validate incoming request for public access
            $validated = $request->validate([
                'email' => 'required|email',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'status' => 'sometimes|string|in:pending,approved,rejected,sent',
            ]);

            $customerEmail = $validated['email'];

            // Get pagination parameters
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 15);
            $status = $request->input('status'); // Optional filter by campaign status

            // Query campaigns by customer email
            $query = JobNotificationCampaign::where('customer_email', $customerEmail)
                ->with(['recipients.business', 'recipients' => function($q) {
                    $q->where('status', 'sent'); // Only get sent notifications
                }]);

            // Filter by status if provided
            if ($status) {
                $query->where('status', $status);
            }

            // Order by creation date (newest first)
            $campaigns = $query->orderByDesc('created_at')
                ->paginate($perPage, ['*'], 'page', $page);

            // Transform campaigns to include job claim status
            $transformedCampaigns = $campaigns->getCollection()->map(function ($campaign) {
                return $this->transformCampaignWithClaimStatus($campaign);
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'campaigns' => $transformedCampaigns,
                    'pagination' => [
                        'current_page' => $campaigns->currentPage(),
                        'per_page' => $campaigns->perPage(),
                        'total' => $campaigns->total(),
                        'last_page' => $campaigns->lastPage(),
                        'from' => $campaigns->firstItem(),
                        'to' => $campaigns->lastItem(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get customer job history: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve job history'
            ], 500);
        }
    }

    /**
     * Get detailed information about a specific job campaign
     * 
     * @param Request $request
     * @param int $campaignId
     * @return JsonResponse
     */
    public function getJobCampaignDetails(Request $request, int $campaignId): JsonResponse
    {
        try {
            // Get authenticated user
            $user = Auth::guard('api')->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => 'Authentication required'
                ], 401);
            }

            // Get campaign and verify ownership
            $campaign = JobNotificationCampaign::with(['recipients.business'])
                ->where('id', $campaignId)
                ->where('customer_email', $user->email)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Campaign not found or access denied'
                ], 404);
            }

            // Get detailed claim information
            $claimDetails = $this->getDetailedClaimInformation($campaign);

            return response()->json([
                'success' => true,
                'data' => [
                    'campaign' => $this->transformCampaignWithClaimStatus($campaign),
                    'claim_details' => $claimDetails,
                    'recipients' => $campaign->recipients->map(function ($recipient) {
                        return [
                            'id' => $recipient->id,
                            'business_name' => $recipient->business_name,
                            'business_email' => $recipient->business_email,
                            'business_phone' => $recipient->business_phone,
                            'business_address' => $recipient->business_address,
                            'distance' => $recipient->distance,
                            'status' => $recipient->status,
                            'sent_at' => $recipient->sent_at,
                            'business' => $recipient->business ? [
                                'id' => $recipient->business->id,
                                'name' => $recipient->business->name,
                                'email' => $recipient->business->email,
                                'phone' => $recipient->business->phone,
                                'address' => $recipient->business->address,
                                'category' => $recipient->business->category,
                            ] : null,
                        ];
                    }),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get job campaign details: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve campaign details'
            ], 500);
        }
    }

    /**
     * Transform campaign with claim status information
     * 
     * @param JobNotificationCampaign $campaign
     * @return array
     */
    private function transformCampaignWithClaimStatus(JobNotificationCampaign $campaign): array
    {
        // Check if job has been claimed
        $jobBooking = JobBooking::where('job_uuid', $campaign->job_id)->first();
        $claimStatus = $this->getClaimStatus($jobBooking);

        return [
            'id' => $campaign->id,
            'job_id' => $campaign->job_id,
            'job_title' => $campaign->job_title,
            'job_description' => $campaign->job_description,
            'job_budget' => $campaign->job_budget,
            'job_zip_code' => $campaign->job_zip_code,
            'job_address' => $campaign->job_address,
            'job_category' => $campaign->job_category,
            'customer_name' => $campaign->customer_name,
            'customer_email' => $campaign->customer_email,
            'customer_phone' => $campaign->customer_phone,
            'service_date' => $campaign->service_date,
            'location' => $campaign->location,
            'service' => $campaign->service,
            'duration' => $campaign->duration,
            'frequency' => $campaign->frequency,
            'currency' => $campaign->currency,
            'budget_min' => $campaign->budget_min,
            'budget_max' => $campaign->budget_max,
            'search_radius' => $campaign->search_radius,
            'business_count' => $campaign->business_count,
            'sent_count' => $campaign->sent_count,
            'failed_count' => $campaign->failed_count,
            'status' => $campaign->status,
            'created_at' => $campaign->created_at,
            'sent_at' => $campaign->sent_at,
            'claim_status' => $claimStatus,
            'recipients_summary' => [
                'total' => $campaign->recipients->count(),
                'pending' => $campaign->recipients->where('status', 'pending')->count(),
                'sent' => $campaign->recipients->where('status', 'sent')->count(),
                'failed' => $campaign->recipients->where('status', 'failed')->count(),
            ],
        ];
    }

    /**
     * Get claim status for a job booking
     * 
     * @param JobBooking|null $jobBooking
     * @return array
     */
    private function getClaimStatus(?JobBooking $jobBooking): array
    {
        if (!$jobBooking) {
            return [
                'claimed' => false,
                'status' => 'not_created',
                'message' => 'Job booking not created yet'
            ];
        }

        // Check if job has been assigned to a provider (eager load provider business)
        $assignedJob = Job::with(['provider.business', 'bid'])
            ->where('job_booking_id', $jobBooking->id)
            ->first();
        
        if ($assignedJob) {
            $provider = $assignedJob->provider;
            $providerBusiness = $provider?->business;
            $bid = $assignedJob->bid;
            
            return [
                'claimed' => true,
                'status' => 'assigned',
                'assigned_at' => $assignedJob->created_at,
                'provider' => $provider ? [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'email' => $provider->email,
                    'phone' => $provider->phone,
                    'business' => $this->formatBusinessInformation($providerBusiness),
                ] : null,
                'bid' => $bid ? [
                    'id' => $bid->id,
                    'amount' => $bid->amount,
                    'estimated_completion_time' => $bid->estimated_completion_time,
                    'description' => $bid->description,
                ] : null,
                'job' => [
                    'id' => $assignedJob->id,
                    'job_uuid' => $assignedJob->job_uuid,
                    'status' => $assignedJob->status,
                    'agreed_amount' => $assignedJob->agreed_amount,
                    'estimated_completion_time' => $assignedJob->estimated_completion_time,
                    'actual_start_time' => $assignedJob->actual_start_time,
                    'actual_completion_time' => $assignedJob->actual_completion_time,
                ]
            ];
        }

        // Check if there are any bids
        $bids = $jobBooking->bids()->with('provider.business')->get();
        if ($bids->count() > 0) {
            return [
                'claimed' => false,
                'status' => 'has_bids',
                'bid_count' => $bids->count(),
                'bids' => $bids->map(function ($bid) {
                    return [
                        'id' => $bid->id,
                        'provider_name' => $bid->provider->name ?? 'Unknown',
                        'amount' => $bid->amount,
                        'status' => $bid->status,
                        'created_at' => $bid->created_at,
                        'provider' => $bid->provider ? [
                            'id' => $bid->provider->id,
                            'name' => $bid->provider->name,
                            'email' => $bid->provider->email,
                            'phone' => $bid->provider->phone,
                            'business' => $this->formatBusinessInformation($bid->provider->business),
                        ] : null,
                    ];
                })
            ];
        }

        return [
            'claimed' => false,
            'status' => 'no_bids',
            'message' => 'No bids received yet'
        ];
    }

    /**
     * Format business information for API response
     *
     * @param Business|null $business
     * @return array|null
     */
    private function formatBusinessInformation(?Business $business): ?array
    {
        if (!$business) {
            return null;
        }

        return [
            'business_uuid' => $business->business_uuid,
            'name' => $business->name,
            'email' => $business->email,
            'phone' => $business->phone,
            'address' => $business->address,
            'category' => $business->category,
            'website' => $business->website,
            'location' => $business->location,
            'latitude' => $business->lat,
            'longitude' => $business->lng,
            'business_hours' => $business->hours,
            'status' => $business->status,
            'services' => $business->services,
            'photos' => $business->photos,
            'reviews' => $business->reviews,
            'zip_code' => $business->zip_code,
        ];
    }

    /**
     * Get detailed claim information for a campaign
     *
     * @param JobNotificationCampaign $campaign
     * @return array
     */
    private function getDetailedClaimInformation(JobNotificationCampaign $campaign): array
    {
        $jobBooking = JobBooking::where('job_uuid', $campaign->job_id)->first();
        
        if (!$jobBooking) {
            return [
                'job_booking_exists' => false,
                'message' => 'Job booking not created yet'
            ];
        }

        $assignedJob = Job::where('job_booking_id', $jobBooking->id)->first();
        $bids = $jobBooking->bids()->with('provider')->get();
        $bookings = $jobBooking->bookings()->with('provider')->get();

        return [
            'job_booking_exists' => true,
            'job_booking' => [
                'id' => $jobBooking->id,
                'job_uuid' => $jobBooking->job_uuid,
                'project_code' => $jobBooking->project_code,
                'status' => $jobBooking->status,
                'created_at' => $jobBooking->created_at,
            ],
            'assigned_job' => $assignedJob ? [
                'id' => $assignedJob->id,
                'job_uuid' => $assignedJob->job_uuid,
                'status' => $assignedJob->status,
                'agreed_amount' => $assignedJob->agreed_amount,
                'estimated_completion_time' => $assignedJob->estimated_completion_time,
                'actual_start_time' => $assignedJob->actual_start_time,
                'actual_completion_time' => $assignedJob->actual_completion_time,
                'provider' => $assignedJob->provider ? [
                    'id' => $assignedJob->provider->id,
                    'name' => $assignedJob->provider->name,
                    'email' => $assignedJob->provider->email,
                    'phone' => $assignedJob->provider->phone,
                ] : null,
            ] : null,
            'bids' => $bids->map(function ($bid) {
                return [
                    'id' => $bid->id,
                    'provider' => $bid->provider ? [
                        'id' => $bid->provider->id,
                        'name' => $bid->provider->name,
                        'email' => $bid->provider->email,
                        'phone' => $bid->provider->phone,
                    ] : null,
                    'amount' => $bid->amount,
                    'status' => $bid->status,
                    'description' => $bid->description,
                    'estimated_completion_time' => $bid->estimated_completion_time,
                    'created_at' => $bid->created_at,
                ];
            }),
            'bookings' => $bookings->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'provider' => $booking->provider ? [
                        'id' => $booking->provider->id,
                        'name' => $booking->provider->name,
                        'email' => $booking->provider->email,
                        'phone' => $booking->provider->phone,
                    ] : null,
                    'status' => $booking->status,
                    'amount' => $booking->amount,
                    'created_at' => $booking->created_at,
                ];
            }),
        ];
    }
}