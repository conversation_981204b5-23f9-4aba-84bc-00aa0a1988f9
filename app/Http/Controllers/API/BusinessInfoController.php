<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Models\JobNotificationRecipient;
use App\Models\Business;
use App\Models\JobBooking;
use App\Models\Bid;
use App\Enums\RoleEnum;
use App\Helpers\Helpers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log as SupportLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class BusinessInfoController extends Controller
{
    /**
     * Get business information from webhook token
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getBusinessInfo(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'job_id' => 'required|string',
                'business_email' => 'nullable|email' // Optional parameter to identify specific business
            ]);

            $token = $request->input('token');
            $jobId = $request->input('job_id');
            $businessEmail = $request->input('business_email');

            // Find campaign by token and job_id
            $campaign = JobNotificationCampaign::where('admin_token', $token)
                ->where('job_id', $jobId)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid token or job not found'
                ], 404);
            }

            // Check if token is expired
            if ($campaign->token_expires_at->isPast()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Token has expired'
                ], 403);
            }

            // Find the specific recipient for this business
            $query = $campaign->recipients();

            if ($businessEmail) {
                // If business email is provided, find specific recipient
                $recipient = $query->where('business_email', $businessEmail)->first();
            } else {
                // Fallback: get first recipient (for backward compatibility)
                $recipient = $query->first();
            }

            if (!$recipient) {
                return response()->json([
                    'success' => false,
                    'error' => 'Business not found for this token'
                ], 404);
            }

            // Check if business already has an account
            $existingUser = \App\Models\User::where('email', $recipient->business_email)->first();

            return response()->json([
                'success' => true,
                'data' => [
                    'business' => [
                        'id' => $recipient->business_id,
                        'name' => $recipient->business_name,
                        'email' => $recipient->business_email,
                        'phone' => $recipient->business_phone,
                        'address' => $recipient->business_address,
                        'distance' => $recipient->distance,
                    ],
                    'job' => [
                        'id' => $campaign->job_id,
                        'title' => $campaign->job_title,
                        'description' => $campaign->job_description,
                        'zip_code' => $campaign->job_zip_code,
                        'category' => $campaign->job_category,
                        'budget' => $campaign->job_budget,
                        'service_date' => $campaign->service_date,
                    ],
                    'has_existing_account' => $existingUser ? true : false,
                    'user_id' => $existingUser ? $existingUser->id : null,
                    'certificates_verified' => $existingUser ? ($existingUser->certificates_status === 'approved') : false,
                    'token_expires_at' => $campaign->token_expires_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get business info from token: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve business information'
            ], 500);
        }
    }

    /**
     * Get job details from token
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getJobDetails(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'token' => 'required|string',
                'job_id' => 'required|string',
                'business_email' => 'nullable|email' // Optional parameter to identify specific business
            ]);

            $token = $request->input('token');
            $jobId = $request->input('job_id');
            $businessEmail = $request->input('business_email');

            // Find campaign by token and job_id
            $campaign = JobNotificationCampaign::where('admin_token', $token)
                ->where('job_id', $jobId)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid token or job not found'
                ], 404);
            }

            // Check if token is expired
            if ($campaign->token_expires_at->isPast()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Token has expired'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'job' => [
                        'id' => $campaign->job_id,
                        'title' => $campaign->job_title,
                        'description' => $campaign->job_description,
                        'zip_code' => $campaign->job_zip_code,
                        'address' => $campaign->job_address,
                        'latitude' => $campaign->job_latitude,
                        'longitude' => $campaign->job_longitude,
                        'category' => $campaign->job_category,
                        'budget' => $campaign->job_budget,
                        'budget_min' => $campaign->budget_min,
                        'budget_max' => $campaign->budget_max,
                        'currency' => $campaign->currency,
                        'service_date' => $campaign->service_date,
                        'location' => $campaign->location,
                        'service' => $campaign->service,
                        'pricing_parameters' => $campaign->pricing_parameters,
                        'note' => $campaign->note,
                        'extra' => $campaign->extra,
                        'price' => $campaign->price,
                        'duration' => $campaign->duration,
                        'frequency' => $campaign->frequency,
                        'created_at' => $campaign->job_created_at,
                    ],
                    'customer' => [
                        'name' => $campaign->customer_name,
                        'email' => $campaign->customer_email,
                        'phone' => $campaign->customer_phone,
                    ],
                    'token_expires_at' => $campaign->token_expires_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get job details from token: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve job details'
            ], 500);
        }
    }

    /**
     * Claim a job by a business recipient using the campaign token
     * Requires authenticated provider matching the business email
     */
    public function claimJob(Request $request): JsonResponse
    {
        try {
            // Validate input
            $validated = $request->validate([
                'token' => 'required|string',
                'job_id' => 'required|string',
                'business_email' => 'required|email',
                'amount' => 'nullable|numeric|min:0.01',
                'description' => 'nullable|string|max:1000',
                'estimated_completion_time' => 'nullable|date|after:now',
            ]);

            $user = Auth::guard('api')->user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => 'Authentication required'
                ], 401);
            }

            // Ensure user is a provider and has a business
            if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
                return response()->json([
                    'success' => false,
                    'error' => 'Only providers can claim jobs'
                ], 403);
            }

            if (!$user->business_uuid) {
                return response()->json([
                    'success' => false,
                    'error' => 'Provider must have a linked business to claim jobs'
                ], 422);
            }

            $token = $validated['token'];
            $jobId = $validated['job_id'];
            $businessEmail = $validated['business_email'];

            // Validate campaign and token
            $campaign = JobNotificationCampaign::where('admin_token', $token)
                ->where('job_id', $jobId)
                ->first();

            if (!$campaign) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid token or job not found'
                ], 404);
            }

            if ($campaign->token_expires_at->isPast()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Token has expired'
                ], 403);
            }

            // Validate recipient
            $recipient = $campaign->recipients()
                ->where('business_email', $businessEmail)
                ->first();

            if (!$recipient) {
                return response()->json([
                    'success' => false,
                    'error' => 'Business not found in campaign recipients'
                ], 404);
            }

            // Ensure the authenticated user matches the recipient business email
            if (strcasecmp($user->email, $businessEmail) !== 0) {
                return response()->json([
                    'success' => false,
                    'error' => 'Authenticated user does not match business email'
                ], 403);
            }

            // Create or find JobBooking using campaign job_id as job_uuid
            $jobBooking = JobBooking::where('job_uuid', $campaign->job_id)->first();

            DB::beginTransaction();

            if (!$jobBooking) {
                $customerUser = $campaign->customer_email
                    ? \App\Models\User::where('email', $campaign->customer_email)->first()
                    : null;

                $jobBooking = JobBooking::create([
                    'job_uuid' => $campaign->job_id,
                    'service_category' => $campaign->job_category,
                    'description' => $campaign->job_description,
                    'address' => $campaign->job_address,
                    'zip_code' => $campaign->job_zip_code,
                    'contact_name' => $campaign->customer_name,
                    'contact_email' => $campaign->customer_email,
                    'contact_phone' => $campaign->customer_phone,
                    'status' => \App\Enums\JobBookingStatusEnum::OPEN->value,
                    'user_id' => $customerUser?->id,
                ]);
            } else {
                // Ensure booking is open for bids
                if (!$jobBooking->isOpen() && !$jobBooking->markAsOpen()) {
                    DB::rollBack();
                    return response()->json([
                        'success' => false,
                        'error' => 'Job booking is not open for claims'
                    ], 422);
                }
            }

            // Prevent duplicate bid from this provider
            $existingBid = $jobBooking->bids()
                ->where('provider_id', $user->id)
                ->first();

            if ($existingBid) {
                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => 'Already claimed',
                    'data' => [
                        'job_booking_id' => $jobBooking->id,
                        'bid_id' => $existingBid->id,
                    ]
                ]);
            }

            // Create a bid representing the claim
            $bid = Bid::create([
                'job_booking_id' => $jobBooking->id,
                'provider_id' => $user->id,
                'amount' => $validated['amount'] ?? ($campaign->job_budget ?? 0.01),
                'description' => $validated['description'] ?? 'Claimed via campaign',
                'estimated_completion_time' => isset($validated['estimated_completion_time'])
                    ? Carbon::parse($validated['estimated_completion_time'])
                    : now()->addDay(),
                'status' => 'requested',
            ]);

            // Update recipient click tracking (optional)
            $recipient->increment('click_count');
            $recipient->last_clicked_at = now();
            $recipient->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Job claimed successfully',
                'data' => [
                    'job_booking_id' => $jobBooking->id,
                    'bid_id' => $bid->id,
                ]
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $ve) {
            return response()->json([
                'success' => false,
                'error' => $ve->getMessage(),
                'details' => $ve->errors(),
            ], 422);
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error('Failed to claim job: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'error' => 'Failed to claim job'
            ], 500);
        }
    }
}