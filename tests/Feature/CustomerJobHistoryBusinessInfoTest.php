<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Business;
use App\Models\JobBooking;
use App\Models\JobNotificationCampaign;
use App\Models\Job;
use App\Models\Bid;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CustomerJobHistoryBusinessInfoTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $customer;
    private $provider;
    private $business;
    private $campaign;
    private $jobBooking;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    private function setupTestData()
    {
        // Create a customer
        $this->customer = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Customer',
            'type' => 'company'
        ]);

        // Create a business
        $this->business = Business::factory()->create([
            'name' => 'Test Cleaning Business',
            'email' => '<EMAIL>',
            'phone' => '******-123-4567',
            'address' => '123 Business St, Test City, TC 12345',
            'category' => 'cleaning',
            'website' => 'https://testbusiness.com',
            'status' => 'active'
        ]);

        // Create a provider linked to the business
        $this->provider = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Test Provider',
            'type' => 'freelancer',
            'business_uuid' => $this->business->business_uuid
        ]);

        // Create a job notification campaign
        $this->campaign = JobNotificationCampaign::factory()->create([
            'job_id' => 'test-job-' . time(),
            'customer_email' => $this->customer->email,
            'customer_name' => $this->customer->name,
            'job_title' => 'Test Cleaning Job',
            'job_description' => 'Test cleaning job description',
            'job_budget' => '150.00',
            'status' => 'sent'
        ]);

        // Create a job booking linked to the campaign
        $this->jobBooking = JobBooking::factory()->create([
            'job_uuid' => $this->campaign->job_id,
            'user_id' => $this->customer->id,
            'description' => 'Test job booking',
            'status' => 'open'
        ]);
    }

    /** @test */
    public function it_returns_empty_business_info_when_no_job_claimed()
    {
        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'campaigns' => [
                        '*' => [
                            'claim_status' => [
                                'claimed',
                                'status',
                                'message'
                            ]
                        ]
                    ]
                ]
            ]);

        $campaigns = $response->json('data.campaigns');
        $this->assertCount(1, $campaigns);
        $this->assertFalse($campaigns[0]['claim_status']['claimed']);
        $this->assertEquals('no_bids', $campaigns[0]['claim_status']['status']);
    }

    /** @test */
    public function it_includes_business_info_when_job_has_bids()
    {
        // Create a bid from the provider
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'amount' => '120.00',
            'status' => 'requested'
        ]);

        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}");

        $response->assertStatus(200);
        
        $campaigns = $response->json('data.campaigns');
        $claimStatus = $campaigns[0]['claim_status'];
        
        $this->assertFalse($claimStatus['claimed']);
        $this->assertEquals('has_bids', $claimStatus['status']);
        $this->assertArrayHasKey('bids', $claimStatus);
        
        $bidData = $claimStatus['bids'][0];
        $this->assertArrayHasKey('provider', $bidData);
        $this->assertArrayHasKey('business', $bidData['provider']);
        
        // Verify business information is included
        $businessData = $bidData['provider']['business'];
        $this->assertEquals($this->business->business_uuid, $businessData['business_uuid']);
        $this->assertEquals($this->business->name, $businessData['name']);
        $this->assertEquals($this->business->email, $businessData['email']);
        $this->assertEquals($this->business->phone, $businessData['phone']);
        $this->assertEquals($this->business->address, $businessData['address']);
        $this->assertEquals($this->business->category, $businessData['category']);
        $this->assertEquals($this->business->website, $businessData['website']);
    }

    /** @test */
    public function it_includes_complete_business_info_when_job_is_assigned()
    {
        // Create a bid from the provider
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $this->provider->id,
            'amount' => '120.00',
            'status' => 'accepted'
        ]);

        // Create an assigned job
        $job = Job::create([
            'job_booking_id' => $this->jobBooking->id,
            'bid_id' => $bid->id,
            'customer_id' => $this->customer->id,
            'provider_id' => $this->provider->id,
            'status' => 'assigned',
            'agreed_amount' => $bid->amount,
            'estimated_completion_time' => now()->addDays(1)
        ]);

        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}");

        $response->assertStatus(200);
        
        $campaigns = $response->json('data.campaigns');
        $claimStatus = $campaigns[0]['claim_status'];
        
        // Verify job is claimed
        $this->assertTrue($claimStatus['claimed']);
        $this->assertEquals('assigned', $claimStatus['status']);
        
        // Verify provider information
        $this->assertArrayHasKey('provider', $claimStatus);
        $providerData = $claimStatus['provider'];
        $this->assertEquals($this->provider->id, $providerData['id']);
        $this->assertEquals($this->provider->name, $providerData['name']);
        $this->assertEquals($this->provider->email, $providerData['email']);
        $this->assertEquals($this->provider->phone, $providerData['phone']);
        
        // Verify complete business information is included
        $this->assertArrayHasKey('business', $providerData);
        $businessData = $providerData['business'];
        $this->assertEquals($this->business->business_uuid, $businessData['business_uuid']);
        $this->assertEquals($this->business->name, $businessData['name']);
        $this->assertEquals($this->business->email, $businessData['email']);
        $this->assertEquals($this->business->phone, $businessData['phone']);
        $this->assertEquals($this->business->address, $businessData['address']);
        $this->assertEquals($this->business->category, $businessData['category']);
        $this->assertEquals($this->business->website, $businessData['website']);
        
        // Verify bid information
        $this->assertArrayHasKey('bid', $claimStatus);
        $bidData = $claimStatus['bid'];
        $this->assertEquals($bid->id, $bidData['id']);
        $this->assertEquals($bid->amount, $bidData['amount']);
        
        // Verify job information
        $this->assertArrayHasKey('job', $claimStatus);
        $jobData = $claimStatus['job'];
        $this->assertEquals($job->id, $jobData['id']);
        $this->assertEquals($job->status, $jobData['status']);
        $this->assertEquals($job->agreed_amount, $jobData['agreed_amount']);
    }

    /** @test */
    public function it_handles_missing_business_gracefully()
    {
        // Create a provider without business
        $providerWithoutBusiness = User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Provider Without Business',
            'type' => 'freelancer',
            'business_uuid' => null
        ]);

        // Create a bid from the provider without business
        $bid = Bid::factory()->create([
            'job_booking_id' => $this->jobBooking->id,
            'provider_id' => $providerWithoutBusiness->id,
            'amount' => '120.00',
            'status' => 'requested'
        ]);

        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}");

        $response->assertStatus(200);
        
        $campaigns = $response->json('data.campaigns');
        $claimStatus = $campaigns[0]['claim_status'];
        
        $this->assertEquals('has_bids', $claimStatus['status']);
        $bidData = $claimStatus['bids'][0];
        $this->assertArrayHasKey('provider', $bidData);
        $this->assertNull($bidData['provider']['business']);
    }

    /** @test */
    public function it_filters_campaigns_by_status()
    {
        // Create another campaign with different status
        JobNotificationCampaign::factory()->create([
            'job_id' => 'test-job-pending-' . time(),
            'customer_email' => $this->customer->email,
            'customer_name' => $this->customer->name,
            'job_title' => 'Pending Test Job',
            'status' => 'pending'
        ]);

        // Test filtering by sent status
        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}&status=sent");
        $response->assertStatus(200);
        $campaigns = $response->json('data.campaigns');
        $this->assertCount(1, $campaigns);
        $this->assertEquals('sent', $campaigns[0]['status']);

        // Test filtering by pending status
        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}&status=pending");
        $response->assertStatus(200);
        $campaigns = $response->json('data.campaigns');
        $this->assertCount(1, $campaigns);
        $this->assertEquals('pending', $campaigns[0]['status']);
    }

    /** @test */
    public function it_validates_required_email_parameter()
    {
        $response = $this->getJson("/api/customer/job-history");
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    /** @test */
    public function it_validates_pagination_parameters()
    {
        $response = $this->getJson("/api/customer/job-history?email={$this->customer->email}&page=0&per_page=101");
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['page', 'per_page']);
    }
}
